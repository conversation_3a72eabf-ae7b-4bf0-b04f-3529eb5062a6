# WhatsApp Gemini Bot Configuration
# Copy this file to .env and fill in your values

# Required: Google Gemini API Key
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Custom bot personality/role
BOT_ROLE=<PERSON><PERSON><PERSON>, an AI developed by <PERSON><PERSON>, replies as a user-friendly female agent.

# Optional: Server port (Railway sets this automatically)
PORT=3000

# Optional: Node environment
NODE_ENV=production
