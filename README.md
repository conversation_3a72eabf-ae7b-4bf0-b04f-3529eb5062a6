# 🤖 WhatsApp Bot - Baileys Edition

## 🚀 **Revolutionary WhatsApp Bot with QueenAmdi Style Implementation**

A next-generation WhatsApp bot powered by **<PERSON><PERSON> library** with **QueenAmdi installer mechanism** for superior performance, stability, and reliability.

## 🎯 **Two-Software Architecture with WhatsApp Web.js**

### **1. QR Generator with Auto-Delivery (`qr-generator.js`)**
- 📱 **Reliable QR Generation** - WhatsApp Web.js LocalAuth for consistent QR codes
- 🔗 **Local Execution** - Run locally for reliable QR generation
- 📲 **Auto-Delivery** - Sends session files directly to your WhatsApp inbox
- 📦 **Smart Packaging** - Packages LocalAuth session files with instructions
- 🖥️ **Web Interface** - Easy-to-use browser interface with real-time updates
- 🔄 **Reset Capability** - Generate fresh sessions anytime
- 📋 **Complete Instructions** - Detailed setup guide included in delivery

### **2. Main Bot (`webjs-session-bot.js`)**
- 🤖 **AI-Powered Responses** - Uses Google Gemini 2.0 Flash for intelligent conversations
- 📁 **Session Upload** - Upload pre-generated LocalAuth session files
- 🖼️ **Media Support** - Handles images with proper WhatsApp Web.js media API
- 📊 **Real-time Dashboard** - Live monitoring and statistics with Socket.IO
- 🚀 **Railway Optimized** - Production-ready deployment with proper Puppeteer config
- ☁️ **Cloud-Friendly** - No QR generation issues on cloud platforms
- 🔧 **Session Management** - Reset, debug, and manage sessions via web interface

## ✨ **Key Advantages**

- ✅ **Official WhatsApp Web.js Implementation** - Follows official documentation
- ✅ **LocalAuth Strategy** - Proper session persistence and management
- ✅ **Auto-Delivery System** - Session files sent directly to your WhatsApp
- ✅ **No Manual Downloads** - Seamless session transfer via WhatsApp
- ✅ **Complete Instructions** - Detailed setup guide included automatically
- ✅ **Instant Connection** - Pre-authenticated sessions work immediately
- ✅ **Media Handling** - Proper WhatsApp Web.js media download and processing
- ✅ **Production Ready** - Optimized for cloud deployment with error handling

## 🚀 Quick Start Guide

### **Step 1: Generate Session Files with Auto-Delivery (Run Locally)**

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Run QR Generator:**
   ```bash
   npm run qr-generator
   ```

3. **Generate and Receive Session:**
   - Visit `http://localhost:3001`
   - Click "Start Session Generation"
   - Scan QR code with WhatsApp
   - **Session files automatically sent to your WhatsApp!** 📲
   - Check your WhatsApp messages for the ZIP file

### **Step 2: Deploy Main Bot (Railway)**

1. **Deploy to Railway:**
   ```bash
   git add .
   git commit -m "Deploy WhatsApp Web.js session bot"
   git push origin main
   ```

2. **Configure Environment:**
   - Set `GEMINI_API_KEY` in Railway dashboard
   - Set `BOT_ROLE` (optional)

3. **Upload Session:**
   - Visit your Railway URL
   - Upload the session ZIP file from your WhatsApp messages
   - Bot connects automatically!

## 🎯 **Available Scripts**

- `npm run qr-generator` - Run QR generation tool (port 3001)
- `npm run session-bot` - Run main bot with session upload (port 3000)
- `npm start` - Default: runs session-bot
- `npm run dev-qr` - Development mode for QR generator
- `npm run dev` - Development mode for main bot

## 🚀 Railway.app Deployment

### Quick Deploy
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template)

### Manual Deployment Steps

1. **Prepare Repository:**
   - Fork or clone this repository to your GitHub account
   - Make any customizations you need

2. **Get Gemini API Key:**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key for later use

3. **Deploy to Railway:**
   - Go to [Railway.app](https://railway.app)
   - Sign up/Login with GitHub
   - Click "New Project" → "Deploy from GitHub repo"
   - Select your repository

4. **Configure Environment Variables:**
   ```
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   BOT_ROLE=Your custom bot personality (optional)
   ```

5. **Access Your Bot:**
   - Railway will provide a public URL
   - Visit the URL to see the dashboard
   - Scan the QR code with WhatsApp
   - Start chatting!

## 🔧 Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GEMINI_API_KEY` | Google Gemini API key | ✅ Yes | None |
| `BOT_ROLE` | Bot personality/instructions | ❌ No | "Roshell, an AI developed by Ayesh..." |
| `PORT` | Server port | ❌ No | 3000 (Railway sets automatically) |
| `NODE_ENV` | Environment mode | ❌ No | production |

## 🏠 Local Development

1. **Clone & Install:**
   ```bash
   git clone <your-repository-url>
   cd whatsapp-genai-bot
   npm install
   ```

2. **Environment Setup:**
   ```bash
   cp .env.example .env
   # Edit .env with your GEMINI_API_KEY
   ```

3. **Start Development:**
   ```bash
   npm run dev  # With auto-restart
   # or
   npm start    # Standard start
   ```

4. **Access Dashboard:**
   - Open `http://localhost:3000`
   - Scan QR code with WhatsApp
   - Send test messages

## 📱 How to Connect WhatsApp

1. Open WhatsApp on your phone
2. Go to **Settings** → **Linked Devices**
3. Tap **"Link a Device"**
4. Scan the QR code from the web dashboard
5. Your bot is now connected!

## 🎯 Bot Capabilities

- **Text Conversations** - Natural language processing and responses
- **Image Analysis** - Describes, analyzes, and answers questions about images
- **📚 PDF Knowledge Base** - Upload PDF files to create a knowledge base for intelligent responses
- **Context Awareness** - Maintains conversation context
- **Error Recovery** - Handles API failures gracefully
- **Multi-language** - Supports multiple languages through Gemini

## 📚 PDF Knowledge Base Feature

### **Upload PDF Documents**
- Upload PDF files through the web interface
- Automatic text extraction and processing
- Support for multi-page documents
- Maximum file size: 20MB per PDF

### **Intelligent Responses**
- Bot uses uploaded PDFs to answer questions
- Searches through document content for relevant information
- Provides accurate responses based on your documents
- Maintains context from multiple uploaded files

### **Knowledge Management**
- View uploaded documents and statistics
- Track word count and page count
- Monitor knowledge base usage
- Delete documents when no longer needed

### **How to Use PDF Knowledge**
1. **Upload PDFs:**
   - Visit the bot dashboard
   - Use the "Upload PDF Knowledge" section
   - Drag & drop or browse for PDF files
   - Wait for processing completion

2. **Ask Questions:**
   - Send messages to the bot via WhatsApp
   - Ask questions related to your uploaded documents
   - Bot will search and provide relevant answers
   - Responses include information from your PDFs

3. **Manage Knowledge:**
   - View knowledge base status in dashboard
   - Monitor document count and total words
   - Track how often knowledge is used
   - Remove outdated documents as needed

## 📊 Dashboard Features

- **Real-time Status** - Connection status and QR code display
- **Live Statistics** - Message counts, errors, uptime, PDF uploads
- **📚 Knowledge Base Management** - Upload, view, and manage PDF documents
- **Activity Logs** - Recent bot activity with timestamps
- **Health Monitoring** - System health and performance metrics
- **Auto-refresh** - Updates every 5 seconds automatically

## 🛠️ API Endpoints

- `GET /` - Main dashboard interface
- `POST /upload-session` - Upload WhatsApp session files
- `POST /upload-pdf` - Upload PDF files for knowledge base
- `GET /knowledge-base` - Get knowledge base information
- `DELETE /knowledge-base/:documentId` - Delete document from knowledge base
- `GET /status` - Bot status and statistics
- `POST /start-bot` - Start the WhatsApp bot
- `POST /stop-bot` - Stop the WhatsApp bot
- `POST /test-bot` - Send test message

## 🔒 Security Features

- **Helmet.js** - Security headers protection
- **CORS** - Cross-origin request protection
- **Input Validation** - Safe handling of user inputs
- **Error Sanitization** - Prevents sensitive data leakage
- **Rate Limiting** - Built-in through Gemini API limits

## 🐛 Troubleshooting

### Common Issues:

1. **QR Code Not Appearing:**
   - Check if GEMINI_API_KEY is set
   - Verify Railway deployment logs
   - Ensure WhatsApp Web is accessible

2. **Bot Not Responding:**
   - Verify GEMINI_API_KEY is valid
   - Check API quota limits
   - Review error logs in dashboard

3. **Connection Lost:**
   - Bot will auto-reconnect
   - Re-scan QR code if needed
   - Check Railway service status

### Debug Mode:
```bash
NODE_ENV=development npm start
```

## 📈 Performance Optimization

- **Memory Management** - Limited log storage (100 entries max)
- **Puppeteer Optimization** - Headless Chrome with minimal resources
- **API Timeout** - 30-second timeout for Gemini requests
- **Graceful Shutdown** - Proper cleanup on termination
- **Health Checks** - Railway health monitoring enabled

## 🤝 Contributing

1. Fork this project
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Issues:** Open an issue in your repository
- **Documentation:** Check this README
- **Railway Support:** [Railway Documentation](https://docs.railway.app)
- **Gemini API:** [Google AI Documentation](https://ai.google.dev)

---

**Made with ❤️ by Ayesh | Powered by Google Gemini AI | Deployed on Railway**
