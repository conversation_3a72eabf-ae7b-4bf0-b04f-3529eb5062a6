#!/bin/bash

# Railway deployment script for WhatsApp Baileys Bot

echo "🚀 Starting Railway deployment for WhatsApp Baileys Bot..."

# Check if git is available
if ! command -v git &> /dev/null; then
    echo "⚠️ Git not found, installing..."
    if command -v apk &> /dev/null; then
        apk add --no-cache git
    elif command -v apt-get &> /dev/null; then
        apt-get update && apt-get install -y git
    elif command -v yum &> /dev/null; then
        yum install -y git
    fi
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo "📦 Node.js version: $NODE_VERSION"

# Check if Node.js version is 20+
MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [ "$MAJOR_VERSION" -lt 20 ]; then
    echo "❌ Node.js 20+ required, found version $NODE_VERSION"
    exit 1
fi

echo "✅ Node.js version check passed"

# Clean npm cache
echo "🧹 Cleaning npm cache..."
npm cache clean --force

# Remove package-lock.json if it exists (to avoid conflicts)
if [ -f "package-lock.json" ]; then
    echo "🗑️ Removing existing package-lock.json..."
    rm package-lock.json
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install --omit=dev --no-audit --no-fund

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create auth directory
echo "📁 Creating auth directory..."
mkdir -p auth_baileys
chmod 755 auth_baileys

# Check if main file exists
if [ -f "src/perfect-baileys.js" ]; then
    echo "✅ Main application file found"
else
    echo "❌ Main application file not found"
    exit 1
fi

echo "🎉 Railway deployment preparation complete!"
echo "🌐 Starting application..."

# Start the application
exec npm start
