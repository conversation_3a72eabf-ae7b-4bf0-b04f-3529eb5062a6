{"name": "whatsapp-genai-bot", "version": "1.0.0", "description": "WhatsApp bot powered by Google Gemini AI for Railway deployment", "main": "src/baileys-whatsapp-bot.js", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"start": "node src/baileys-whatsapp-bot.js", "bot": "node src/baileys-whatsapp-bot.js", "session-generator": "node src/baileys-session-generator.js", "dev": "nodemon src/baileys-whatsapp-bot.js", "dev-generator": "nodemon src/baileys-session-generator.js", "compare": "node src/compare-generators.js", "test": "echo \"No tests specified\" && exit 0", "lint": "echo \"No linting configured\" && exit 0"}, "keywords": ["whatsapp", "bot", "gemini", "ai", "railway"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@google/generative-ai": "0.24.1", "@whiskeysockets/baileys": "6.7.19", "adm-zip": "0.5.16", "archiver": "^6.0.1", "axios": "^1.6.0", "chalk": "5.6.0", "cors": "^2.8.5", "dotenv": "16.6.1", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "2.0.2", "puppeteer": "18.2.1", "qrcode": "^1.5.4", "socket.io": "^4.7.5", "whatsapp-web.js": "^1.23.0"}, "devDependencies": {"nodemon": "^3.0.2"}}