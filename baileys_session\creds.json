{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "yBgF9knAuiEGRlDPmvRfzxjBNoHKkuLRp7/fGxgVV2U="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "Ax/OgydSib1ygci7JsOeD6n2a2Qd7Up3HjvJLAhmv30="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "8PZp4o1ODQKubyQ8Xxlw1WuynuIx8kop1IXomAGpV3g="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "vGLGj75/v4ohoemnKE7gVGca6eBa5keARc4FOX8JMUM="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "mBLhnSgISCL9LvFfxs9J23bZ4mYiazxeN5zxtLYt+VI="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "1wW2eYdwvrU2Ed9F9/zVpbIlaH2H7mhnNMdGInxKFVE="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "6PSAG/CuGSKw86e34LKWtNoT2BBDCEz4NyOirBVqRG0="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "vY0FlL9Ma3qL+yyIFW8LNshSBnvaloZNhZ/xygFwplM="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "/kpO8OuFxDUctgM8stiPc6Q273XY2J2bI+lFzVMgz2gNwPYp6s6if+fffpRW7q7+kdQ6tNljQNCsjGteB4ymBw=="}, "keyId": 1}, "registrationId": 156, "advSecretKey": "mN1LI880uDT9sgSla9IqOSINPd0CYYE95EOhqok1yIk=", "processedHistoryMessages": [], "nextPreKeyId": 31, "firstUnuploadedPreKeyId": 31, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CLOOzpIEEP+t2cUGGAEgACgA", "accountSignatureKey": "wPRAoadgyFGNrFid1lLc+DeD4FNwyGlf8oT1103ImXs=", "accountSignature": "UYbX2SJYeNHaYF8VNFPLxL8VbWWe71RmY16y94pwfPDcM2eVnQ0UXFq0LFPJ/GZXO9w/gjNut9IF6n3eTG8PgQ==", "deviceSignature": "Otq2PX9N7I303WoxgVyi12qsviiqISY2xwBAmz3L/l0KnvmIpNq3FCnQfA7lB8KgKzKgmwAKInt6WDEn5akxBw=="}, "me": {"id": "***********:<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "lid": "*************:14@lid"}, "signalIdentities": [{"identifier": {"name": "***********:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BcD0QKGnYMhRjaxYndZS3Pg3g+BTcMhpX/KE9ddNyJl7"}}], "platform": "smbi", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CAgIBQ=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "3R9Z39", "myAppStateKeyId": "AAAAAIeZ"}