#!/bin/bash
# Railway deployment script

echo "🚀 Starting Railway deployment..."

# Install dependencies
echo "📦 Installing dependencies..."
if [ -f package-lock.json ]; then
    echo "Using npm ci with existing package-lock.json"
    npm ci --omit=dev
else
    echo "Using npm install (no package-lock.json found)"
    npm install --omit=dev
fi

# Clean npm cache
echo "🧹 Cleaning npm cache..."
npm cache clean --force

echo "✅ Deployment preparation complete!"
echo "🌐 Starting application..."

# Start the application
npm start
