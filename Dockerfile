# Use Node.js 20 LTS version
FROM node:20-alpine

# Install necessary packages for Baileys WhatsApp Bot
RUN apk add --no-cache \
    ca-certificates \
    git \
    python3 \
    make \
    g++

# Set environment variables for Baileys
ENV NODE_ENV=production \
    SESSION_DIR=/tmp/baileys_session \
    UPLOADS_DIR=/tmp/uploads \
    MEDIA_DIR=/tmp/media_temp

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --omit=dev && npm cache clean --force

# Copy application code
COPY . .

# Create directories in /tmp with proper permissions (writable by all users)
RUN mkdir -p /tmp/baileys_session /tmp/uploads /tmp/media_temp && \
    chmod -R 777 /tmp/baileys_session /tmp/uploads /tmp/media_temp && \
    chown -R node:node /app && \
    chmod -R 755 /app

# Switch to non-root user
USER node

# Expose port (Railway will set PORT env var)
EXPOSE 3004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node -e "require('http').get('http://localhost:' + (process.env.PORT || 3004) + '/status', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["npm", "start"]
