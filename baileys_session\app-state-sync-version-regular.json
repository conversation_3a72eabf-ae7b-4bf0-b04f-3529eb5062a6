{"version": 3, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "XHTSlk1WAU5ZYK2VkxR9U5PrglbikMMXDhutJBgG/mZ2V94p1FcSAcQsiFb5dkRorcec86svFNxpy8rBoJ1jjxylsJklFOtkC/uLUCHYAwO4b6tPx8o5cvyKJ+iyA9zU26cRnetrRegHcbfaPn03pkIsFcHb5Bh4K7chmKtQG0Y="}, "indexValueMap": {"1B3ixwGkBWq24XiLPjzGl1u9ddpj3f4MCb9Sebnc9C4=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "AsLGqNd/agyh8DNk3vc8gbQE9ApTg7jSHQJGqwEhYP0="}}, "Lfyf+1kUDfmRZvec4aeoNXU3IPsCF/g+TGy4mfzn5CY=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "h905X7yZYg4Nx/WFkoXg/mYfxNPx/pBdkxSuk3eW8SQ="}}, "OmfoW1BJ/zt/Djl9LTTK2gCcUMkayGWPlWyGwpdfDMw=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "PcvCnnots483A3bA+to4NmEEeb1MU+ldhFTlj6McsbY="}}, "nM8Q9cfxYbFTaqTPFUWfSAQc+7u5oh0f1bj7TzMAV60=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "a2VXJ9TSz8LdPNYWEXKnM9G5SCgiqAe/jiqOHUD4eMk="}}, "Wkj7HSnZHBXR45Val93o/dw+S+iHWVqsScA9DqMWMCI=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "QbNRGjdGyfb92qQG9YYmXzLMyTT1mfRxWu3PcqTiOnQ="}}, "XhgZ6jl9pJ8pDTBWulv3zzPd0DGIkDQ/BxvnMmbWQl8=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "G+TB3WRvzFZxHxLK3ZzQF586jxH+QoRTLzvnsEnhPYQ="}}, "r5VbXoeeMAc60+Psko2lB/djI97uoS94gcRKCpfysqg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "JgWV96v8t9WkkDXo3aeHCNZSkFbYacYoANldQDw5xOA="}}, "2b1k87ZymAZZ8wdinQ2Cg143oJVH5cPnSheyDFjGMuw=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "I32gMMcJyKOgawNVpiDMuWzGuVLyqnOwEf3PwQl3E60="}}, "yOHyZUnInyEzs0Es3JfKt8xWiArDT8+K2HZErxD8atk=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "tTCYdZBeQC75inYEhcwIUllDX2vwByU7zlFAWtTkAG4="}}, "Up6rFjifVdNztl6viBEgYP/FMJmXameGn9GVJhA8KHE=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "8uRfFjU/VeDj+zgVwx8UPigyx8w6AYuPdg7aGVQSJMw="}}, "PlVb14416LqLU5QsN4eZgnkbIi9cb5eZz3yc0KZdZ10=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "gbTmX92CS2crN4a18mj4lOvYH+xrwCuVZEGmIt3bdHY="}}, "0XY/BVqrCfCflc/quEcaUKqKp7onfgovomqIOFG+eJU=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "G+kYz2Pebh6Wd3nslCCx3Yl5+g+4iG1hhUxGyDgacRY="}}, "Wq/duQBZT0Z4pYwAnpP5+lhQ+N9SyT/ZpAgh6LR2FIg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "0IuEMU1rwXWHtwa5+9AYDCNM5QkoHEDFpsnOwjMITLI="}}}}