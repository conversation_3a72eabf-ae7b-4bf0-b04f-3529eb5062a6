const express = require('express');
const multer = require('multer');
const AdmZip = require('adm-zip');
const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');
const { default: makeWASocket, DisconnectReason, useMultiFileAuthState, downloadMediaMessage } = require('@whiskeysockets/baileys');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3004;

// Configuration - Use environment variable for session directory in production
const sessionDir = process.env.SESSION_DIR || path.join(__dirname, '..', 'baileys_session');
const uploadsDir = process.env.UPLOADS_DIR || path.join(__dirname, '..', 'uploads');
const mediaDir = process.env.MEDIA_DIR || path.join(__dirname, '..', 'media_temp');
const knowledgeDir = process.env.KNOWLEDGE_DIR || path.join(__dirname, '..', 'knowledge_base');

// Gemini AI Configuration
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const BOT_ROLE = process.env.BOT_ROLE || 'Roshell, an AI developed by Ayesh, replies as a user-friendly female agent.';

let genAI = null;
let model = null;

// Initialize Gemini AI
function initializeGemini() {
  if (!GEMINI_API_KEY || GEMINI_API_KEY === 'your_gemini_api_key_here') {
    addLog('⚠️ GEMINI_API_KEY not configured. Bot will work in echo mode.');
    addLog('💡 Set your Gemini API key in .env file to enable AI responses');
    return false;
  }

  try {
    genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });
    addLog('✅ Gemini AI initialized successfully');
    return true;
  } catch (error) {
    addLog(`❌ Failed to initialize Gemini AI: ${error.message}`);
    return false;
  }
}

// Application state
let sock = null;
let isConnected = false;
let logs = [];
let retryCount = 0;
const maxRetries = 5;
let aiEnabled = false;

// Statistics
let stats = {
  messagesReceived: 0,
  messagesProcessed: 0,
  aiResponses: 0,
  imagesProcessed: 0,
  imageDescriptions: 0,
  errors: 0,
  startTime: new Date(),
  pdfUploads: 0,
  knowledgeQueries: 0
};

// Knowledge base storage
let knowledgeBase = {
  documents: [],
  lastUpdated: null
};

// Ensure directories exist with proper permissions
function ensureDirectories() {
  [sessionDir, uploadsDir, mediaDir, knowledgeDir].forEach(dir => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
        addLog(`📁 Created directory: ${path.basename(dir)}`);
      }
    } catch (error) {
      addLog(`⚠️ Could not create directory ${path.basename(dir)}: ${error.message}`);
      // Try to continue anyway - some directories might be optional
    }
  });
}

// Logging utility
function addLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  logs.push(logMessage);
  console.log(logMessage);

  // Keep only last 100 logs
  if (logs.length > 100) {
    logs = logs.slice(-100);
  }
}

// PDF processing functions
async function processPDFFile(filePath, originalName) {
  try {
    addLog(`📄 Processing PDF: ${originalName}`);

    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdfParse(dataBuffer);

    const extractedText = data.text;
    const pageCount = data.numpages;

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('No text content found in PDF');
    }

    // Store the document in knowledge base
    const document = {
      id: Date.now().toString(),
      filename: originalName,
      content: extractedText,
      pageCount: pageCount,
      uploadedAt: new Date(),
      wordCount: extractedText.split(/\s+/).length
    };

    knowledgeBase.documents.push(document);
    knowledgeBase.lastUpdated = new Date();

    // Save knowledge base to file
    await saveKnowledgeBase();

    stats.pdfUploads++;
    addLog(`✅ PDF processed: ${originalName} (${pageCount} pages, ${document.wordCount} words)`);

    return document;
  } catch (error) {
    addLog(`❌ Error processing PDF ${originalName}: ${error.message}`);
    throw error;
  }
}

async function saveKnowledgeBase() {
  try {
    const knowledgeFile = path.join(knowledgeDir, 'knowledge_base.json');
    fs.writeFileSync(knowledgeFile, JSON.stringify(knowledgeBase, null, 2));
    addLog(`💾 Knowledge base saved (${knowledgeBase.documents.length} documents)`);
  } catch (error) {
    addLog(`❌ Error saving knowledge base: ${error.message}`);
  }
}

async function loadKnowledgeBase() {
  try {
    const knowledgeFile = path.join(knowledgeDir, 'knowledge_base.json');
    if (fs.existsSync(knowledgeFile)) {
      const data = fs.readFileSync(knowledgeFile, 'utf8');
      knowledgeBase = JSON.parse(data);
      addLog(`📚 Loaded knowledge base (${knowledgeBase.documents.length} documents)`);
    }
  } catch (error) {
    addLog(`❌ Error loading knowledge base: ${error.message}`);
    // Initialize empty knowledge base on error
    knowledgeBase = { documents: [], lastUpdated: null };
  }
}

// Generate AI response using Gemini with knowledge base
async function generateAIResponse(userMessage, fromUser) {
  if (!aiEnabled || !model) {
    // Fallback to echo mode
    return `🤖 *Echo Mode Response:*\n\nYou said: "${userMessage}"\n\n✅ Bot is working! (AI not configured)\n🕐 Time: ${new Date().toLocaleString()}\n🔧 Powered by Baileys`;
  }

  try {
    addLog(`🤖 Generating AI response for: ${userMessage.substring(0, 50)}...`);

    // Search for relevant knowledge
    const relevantKnowledge = searchKnowledgeBase(userMessage);
    let knowledgeContext = '';

    if (relevantKnowledge.length > 0) {
      stats.knowledgeQueries++;
      knowledgeContext = `\n\nRelevant knowledge from uploaded documents:\n${relevantKnowledge.map(doc =>
        `Document: ${doc.filename}\nContent: ${doc.relevantText}`
      ).join('\n\n')}`;
      addLog(`📚 Using knowledge from ${relevantKnowledge.length} document(s)`);
    }

    const prompt = `${BOT_ROLE}

User message: "${userMessage}"
From: ${fromUser}${knowledgeContext}

Please respond naturally and helpfully. If you have relevant knowledge from the uploaded documents, use it to provide accurate and detailed information. Keep responses concise but informative.`;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    if (text && text.trim()) {
      stats.aiResponses++;
      addLog(`✅ AI response generated (${text.length} chars)`);
      return text.trim();
    } else {
      throw new Error('Empty response from AI');
    }
  } catch (error) {
    stats.errors++;
    addLog(`❌ AI response error: ${error.message}`);

    // Fallback response
    return `🤖 *AI Response Error*\n\nSorry, I'm having trouble processing your message right now. Please try again later.\n\n📝 Your message: "${userMessage}"\n🕐 Time: ${new Date().toLocaleString()}`;
  }
}

// Search knowledge base for relevant content
function searchKnowledgeBase(query) {
  if (!knowledgeBase.documents || knowledgeBase.documents.length === 0) {
    return [];
  }

  const queryLower = query.toLowerCase();
  const relevantDocs = [];

  knowledgeBase.documents.forEach(doc => {
    const content = doc.content.toLowerCase();

    // Simple keyword matching - look for query words in content
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    let relevanceScore = 0;
    let relevantSentences = [];

    // Split content into sentences
    const sentences = doc.content.split(/[.!?]+/).filter(s => s.trim().length > 10);

    sentences.forEach(sentence => {
      const sentenceLower = sentence.toLowerCase();
      let sentenceScore = 0;

      queryWords.forEach(word => {
        if (sentenceLower.includes(word)) {
          sentenceScore++;
        }
      });

      if (sentenceScore > 0) {
        relevanceScore += sentenceScore;
        relevantSentences.push({
          text: sentence.trim(),
          score: sentenceScore
        });
      }
    });

    if (relevanceScore > 0) {
      // Sort sentences by relevance and take top 3
      relevantSentences.sort((a, b) => b.score - a.score);
      const topSentences = relevantSentences.slice(0, 3).map(s => s.text);

      relevantDocs.push({
        filename: doc.filename,
        relevantText: topSentences.join('. '),
        score: relevanceScore
      });
    }
  });

  // Sort by relevance score and return top 2 documents
  return relevantDocs.sort((a, b) => b.score - a.score).slice(0, 2);
}

// Analyze image using Gemini Vision
async function analyzeImage(imageBuffer, userMessage = '', fromUser = '') {
  if (!aiEnabled || !model) {
    return `🖼️ *Image Received (Echo Mode)*\n\nI can see you sent an image, but AI vision is not configured.\n\n✅ Bot is working! (AI not configured)\n🕐 Time: ${new Date().toLocaleString()}\n🔧 Powered by Baileys`;
  }

  try {
    addLog(`🖼️ Analyzing image with Gemini Vision...`);
    stats.imagesProcessed++;

    // Convert buffer to base64
    const base64Image = imageBuffer.toString('base64');

    // Create the prompt for image analysis
    const prompt = `${BOT_ROLE}

The user has sent you an image${userMessage ? ` with the message: "${userMessage}"` : ''}.
From: ${fromUser}

Please analyze this image and provide a detailed, helpful description. Include:
1. What you see in the image (objects, people, scenes, text, etc.)
2. Colors, composition, and visual elements
3. Any relevant context or interesting details
4. If there's text in the image, transcribe it
5. Answer any specific questions the user might have about the image

Be descriptive but conversational, and respond as Roshell would.`;

    const imagePart = {
      inlineData: {
        data: base64Image,
        mimeType: 'image/jpeg' // Assume JPEG, Gemini handles various formats
      }
    };

    const result = await model.generateContent([prompt, imagePart]);
    const response = result.response;
    const text = response.text();

    if (text && text.trim()) {
      stats.imageDescriptions++;
      addLog(`✅ Image analysis completed (${text.length} chars)`);
      return `🖼️ *Image Analysis*\n\n${text.trim()}`;
    } else {
      throw new Error('Empty response from AI');
    }
  } catch (error) {
    stats.errors++;
    addLog(`❌ Image analysis error: ${error.message}`);

    // Fallback response
    return `🖼️ *Image Analysis Error*\n\nSorry, I'm having trouble analyzing your image right now. Please try again later.\n\n${userMessage ? `📝 Your message: "${userMessage}"\n` : ''}🕐 Time: ${new Date().toLocaleString()}`;
  }
}

// Configure multer for session file uploads
const sessionStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureDirectories();
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    cb(null, `session-${Date.now()}.zip`);
  }
});

const sessionUpload = multer({
  storage: sessionStorage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/zip' || file.originalname.endsWith('.zip')) {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  }
});

// Configure multer for PDF uploads
const pdfStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureDirectories();
    cb(null, knowledgeDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `pdf-${Date.now()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const pdfUpload = multer({
  storage: pdfStorage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf' || file.originalname.toLowerCase().endsWith('.pdf')) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed!'), false);
    }
  },
  limits: {
    fileSize: 20 * 1024 * 1024 // 20MB limit for PDFs
  }
});

// Extract session files from uploaded ZIP
async function extractSessionFiles(zipPath) {
  try {
    addLog('📦 Extracting session files from ZIP...');

    // Clear existing session directory with better error handling
    if (fs.existsSync(sessionDir)) {
      try {
        // Try to remove individual files first
        const files = fs.readdirSync(sessionDir);
        for (const file of files) {
          const filePath = path.join(sessionDir, file);
          try {
            if (fs.statSync(filePath).isDirectory()) {
              fs.rmSync(filePath, { recursive: true, force: true });
            } else {
              fs.unlinkSync(filePath);
            }
          } catch (fileError) {
            addLog(`⚠️ Could not remove ${file}: ${fileError.message}`);
          }
        }
      } catch (dirError) {
        addLog(`⚠️ Could not clear session directory: ${dirError.message}`);
        // Try alternative approach - create a backup name
        const backupDir = sessionDir + '_backup_' + Date.now();
        try {
          fs.renameSync(sessionDir, backupDir);
          addLog(`📁 Moved existing session to ${backupDir}`);
        } catch (renameError) {
          addLog(`❌ Could not backup existing session: ${renameError.message}`);
        }
      }
    }

    // Ensure session directory exists with proper permissions
    try {
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true, mode: 0o755 });
      }
    } catch (mkdirError) {
      addLog(`❌ Could not create session directory: ${mkdirError.message}`);
      return false;
    }

    // Extract ZIP file
    const zip = new AdmZip(zipPath);
    const entries = zip.getEntries();

    let extractedFiles = 0;
    entries.forEach(entry => {
      if (!entry.isDirectory) {
        try {
          // Extract to session directory, handling nested paths
          let extractPath = entry.entryName;

          // If files are in a subdirectory like 'baileys_session/', extract them to root
          if (extractPath.includes('baileys_session/')) {
            extractPath = extractPath.replace(/.*baileys_session\//, '');
          }

          if (extractPath && !extractPath.includes('/')) {
            const fullPath = path.join(sessionDir, extractPath);

            // Extract file content and write manually for better permission control
            const fileData = entry.getData();
            fs.writeFileSync(fullPath, fileData, { mode: 0o644 });
            extractedFiles++;
            addLog(`📄 Extracted: ${extractPath}`);
          }
        } catch (extractError) {
          addLog(`⚠️ Could not extract ${entry.entryName}: ${extractError.message}`);
        }
      }
    });

    addLog(`✅ Extracted ${extractedFiles} session files`);

    // Clean up uploaded ZIP
    try {
      fs.unlinkSync(zipPath);
      addLog('🗑️ Cleaned up uploaded ZIP file');
    } catch (cleanupError) {
      addLog(`⚠️ Could not clean up ZIP file: ${cleanupError.message}`);
    }

    return extractedFiles > 0;
  } catch (error) {
    addLog(`❌ Error extracting session files: ${error.message}`);
    addLog(`📋 Error details: ${error.stack}`);
    return false;
  }
}

// Initialize Baileys WhatsApp connection
async function initializeWhatsApp() {
  try {
    if (sock) {
      try {
        sock.end();
      } catch (error) {
        addLog(`⚠️ Cleanup warning: ${error.message}`);
      }
      sock = null;
    }
    
    ensureDirectories();
    addLog('🚀 Initializing Baileys WhatsApp Bot...');

    // Initialize Gemini AI
    aiEnabled = initializeGemini();
    
    // Check if session files exist
    const sessionFiles = fs.existsSync(sessionDir) ? fs.readdirSync(sessionDir) : [];
    if (sessionFiles.length > 0) {
      addLog(`📁 Found ${sessionFiles.length} session files, attempting to connect...`);
    } else {
      addLog('📁 No session files found, will need to scan QR code');
    }
    
    // Use multi-file auth state
    const { state, saveCreds } = await useMultiFileAuthState(sessionDir);
    
    // Create Baileys socket with proper logger
    sock = makeWASocket({
      auth: state,
      printQRInTerminal: false,
      logger: {
        level: 'silent',
        trace: () => {},
        debug: () => {},
        info: () => {},
        warn: () => {},
        error: () => {},
        fatal: () => {},
        child: () => ({
          level: 'silent',
          trace: () => {},
          debug: () => {},
          info: () => {},
          warn: () => {},
          error: () => {},
          fatal: () => {}
        })
      }
    });
    
    // Handle connection updates
    sock.ev.on('connection.update', async (update) => {
      const { connection, lastDisconnect, qr } = update;
      
      if (qr) {
        addLog('📱 QR Code generated - scan with WhatsApp to connect');
        // In a real implementation, you might want to display this QR code
      }
      
      if (connection === 'close') {
        const shouldReconnect = (lastDisconnect?.error)?.output?.statusCode !== DisconnectReason.loggedOut;
        
        isConnected = false;
        
        if (sock) {
          sock = null;
        }
        
        addLog(`🔌 Connection closed: ${lastDisconnect?.error?.message || 'Unknown reason'}`);
        
        if (shouldReconnect) {
          retryCount++;
          
          if (retryCount >= maxRetries) {
            addLog(`❌ Maximum retry attempts (${maxRetries}) reached. Stopping automatic retries.`);
            retryCount = 0;
            return;
          }
          
          addLog(`🔄 Reconnecting... Attempt ${retryCount}/${maxRetries}`);
          setTimeout(() => {
            if (!sock) {
              initializeWhatsApp();
            }
          }, 5000);
        } else {
          addLog('🛑 Logged out, not reconnecting');
        }
      } else if (connection === 'open') {
        isConnected = true;
        retryCount = 0;
        
        addLog('✅ WhatsApp connected successfully!');
        addLog(`📱 Connected as: ${sock.user.name || sock.user.id}`);
        
        // Send a welcome message to self
        try {
          const aiStatus = aiEnabled ? '🤖 Gemini AI Enabled' : '🔄 Echo Mode (AI not configured)';
          await sock.sendMessage(sock.user.id, {
            text: `🤖 *WhatsApp Bot Connected!*\n\n` +
                  `✅ Bot is now online and ready\n` +
                  `📅 Connected at: ${new Date().toLocaleString()}\n` +
                  `🔧 Using Baileys library\n` +
                  `🚀 Powered by QueenAmdi mechanism\n` +
                  `${aiStatus}\n\n` +
                  `Send any message to test the bot!`
          });
          addLog('📤 Welcome message sent');
        } catch (msgError) {
          addLog(`⚠️ Could not send welcome message: ${msgError.message}`);
        }
      }
    });
    
    // Handle credentials update
    sock.ev.on('creds.update', saveCreds);
    
    // Handle incoming messages
    sock.ev.on('messages.upsert', async (m) => {
      const message = m.messages[0];
      if (!message.key.fromMe && m.type === 'notify') {
        try {
          const from = message.key.remoteJid;
          const messageText = message.message?.conversation ||
                            message.message?.extendedTextMessage?.text ||
                            message.message?.imageMessage?.caption || '';

          // Check if message contains an image
          const hasImage = message.message?.imageMessage;

          if (hasImage) {
            stats.messagesReceived++;
            addLog(`📨 Received image from ${from}${messageText ? ` with caption: ${messageText.substring(0, 50)}...` : ''}`);

            try {
              // Download the image
              const buffer = await downloadMediaMessage(message, 'buffer', {});

              // Analyze image with Gemini Vision
              const response = await analyzeImage(buffer, messageText, from);

              await sock.sendMessage(from, { text: response });
              stats.messagesProcessed++;
              addLog(`📤 Sent image analysis to ${from}`);
            } catch (imageError) {
              addLog(`❌ Error processing image: ${imageError.message}`);
              const errorResponse = `🖼️ *Image Processing Error*\n\nSorry, I couldn't analyze your image. Please try sending it again.\n\n🕐 Time: ${new Date().toLocaleString()}`;
              await sock.sendMessage(from, { text: errorResponse });
            }
          } else if (messageText) {
            stats.messagesReceived++;
            addLog(`📨 Received text message from ${from}: ${messageText.substring(0, 100)}...`);

            // Generate AI response for text
            const response = await generateAIResponse(messageText, from);

            await sock.sendMessage(from, { text: response });
            stats.messagesProcessed++;
            addLog(`📤 Sent AI response to ${from}`);
          }
        } catch (error) {
          stats.errors++;
          addLog(`❌ Error handling message: ${error.message}`);

          // Send error message to user
          try {
            const errorResponse = `🚫 *Error Processing Message*\n\nSorry, I encountered an error while processing your message. Please try again.\n\n🕐 Time: ${new Date().toLocaleString()}`;
            await sock.sendMessage(from, { text: errorResponse });
          } catch (sendError) {
            addLog(`❌ Failed to send error message: ${sendError.message}`);
          }
        }
      }
    });
    
    addLog('🔄 Baileys socket initialized, waiting for connection...');
    
  } catch (error) {
    addLog(`❌ Error initializing WhatsApp: ${error.message}`);
    
    if (sock) {
      try {
        sock.end();
      } catch (destroyError) {
        // Ignore cleanup errors
      }
      sock = null;
    }
    
    retryCount++;
    
    if (retryCount >= maxRetries) {
      addLog(`❌ Maximum retry attempts (${maxRetries}) reached. Stopping automatic retries.`);
      retryCount = 0;
      return;
    }
    
    addLog(`🔄 Retrying in 10 seconds... Attempt ${retryCount}/${maxRetries}`);
    setTimeout(() => {
      if (!sock) {
        initializeWhatsApp();
      }
    }, 10000);
  }
}

// Express middleware
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>WhatsApp Bot (Baileys) - QueenAmdi Style</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }
        .container { 
          background: white; 
          border-radius: 20px; 
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 900px; 
          width: 100%; 
          overflow: hidden;
        }
        .header {
          background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .content { padding: 40px; }
        .section { 
          background: #f8f9fa; 
          border-radius: 15px; 
          padding: 30px; 
          margin: 20px 0; 
          border-left: 5px solid #25D366;
        }
        .section h3 { color: #333; margin-bottom: 15px; font-size: 1.3em; }
        .section p { color: #666; line-height: 1.6; margin-bottom: 15px; }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          margin: 10px;
          background: #25D366;
          color: white;
          text-decoration: none;
          border-radius: 8px;
          font-weight: bold;
          border: none;
          cursor: pointer;
          font-size: 16px;
          transition: all 0.3s;
        }
        .btn:hover { background: #128C7E; transform: translateY(-2px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .btn-primary { background: #007bff; }
        .btn-primary:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .status { 
          padding: 20px; 
          border-radius: 10px; 
          margin: 20px 0; 
          text-align: center;
        }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .logs { 
          background: #1e1e1e; 
          color: #00ff00; 
          padding: 20px; 
          border-radius: 10px; 
          font-family: 'Courier New', monospace; 
          max-height: 300px; 
          overflow-y: auto; 
          margin: 20px 0;
        }
        .logs div { margin: 2px 0; font-size: 12px; }
        .upload-area {
          border: 2px dashed #ddd;
          border-radius: 10px;
          padding: 30px;
          text-align: center;
          margin: 20px 0;
          transition: all 0.3s;
        }
        .upload-area:hover { border-color: #25D366; background: #f8f9fa; }
        .upload-area.dragover { border-color: #25D366; background: #e8f5e8; }
        .feature-badge {
          display: inline-block;
          background: #007bff;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          margin: 2px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🤖 WhatsApp Bot</h1>
          <p>Powered by Baileys - QueenAmdi Style Implementation</p>
          <div>
            <span class="feature-badge">🔥 Baileys Library</span>
            <span class="feature-badge">⚡ Direct WebSocket</span>
            <span class="feature-badge">🛡️ No Browser</span>
            <span class="feature-badge">👑 QueenAmdi Style</span>
            <span class="feature-badge" id="ai-status">🤖 AI Status</span>
            <span class="feature-badge">🖼️ Vision Enabled</span>
          </div>
        </div>
        
        <div class="content">
          <div id="status-section">
            <div id="status-display"></div>
          </div>
          
          <div class="section">
            <h3>📁 Upload Session Files</h3>
            <p>Upload a ZIP file containing your WhatsApp session files to connect without scanning QR code.</p>
            <div class="upload-area" id="upload-area">
              <input type="file" id="file-input" accept=".zip" style="display: none;">
              <div id="upload-content">
                <h4>📦 Drop ZIP file here or click to browse</h4>
                <p>Supported: ZIP files with Baileys session data</p>
                <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                  📁 Choose Session ZIP File
                </button>
              </div>
            </div>
          </div>

          <div class="section">
            <h3>📚 Upload PDF Knowledge</h3>
            <p>Upload PDF files to add knowledge to the bot. The bot will use this information to answer questions.</p>
            <div class="upload-area" id="pdf-upload-area">
              <input type="file" id="pdf-file-input" accept=".pdf" style="display: none;">
              <div id="pdf-upload-content">
                <h4>📄 Drop PDF file here or click to browse</h4>
                <p>Supported: PDF files (max 20MB)</p>
                <button class="btn btn-primary" onclick="document.getElementById('pdf-file-input').click()">
                  📄 Choose PDF File
                </button>
              </div>
            </div>
            <div id="knowledge-base-info" style="margin-top: 15px;">
              <h4>📊 Knowledge Base Status</h4>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 10px 0;">
                <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                  <span style="font-size: 12px; color: #666;">Documents</span><br>
                  <span id="kb-documents" style="font-size: 20px; font-weight: bold; color: #25D366;">0</span>
                </div>
                <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; text-align: center;">
                  <span style="font-size: 12px; color: #666;">Total Words</span><br>
                  <span id="kb-words" style="font-size: 20px; font-weight: bold; color: #1976d2;">0</span>
                </div>
                <div style="background: #f3e5f5; padding: 10px; border-radius: 8px; text-align: center;">
                  <span style="font-size: 12px; color: #666;">Queries</span><br>
                  <span id="kb-queries" style="font-size: 20px; font-weight: bold; color: #7b1fa2;">0</span>
                </div>
              </div>
              <button class="btn btn-primary" onclick="viewKnowledgeBase()" style="margin-top: 10px;">
                📚 View Knowledge Base
              </button>
            </div>
          </div>

          <div class="section">
            <h3>🚀 Bot Controls</h3>
            <p>Manage your WhatsApp bot connection and settings.</p>
            <div style="text-align: center;">
              <button class="btn" onclick="startBot()">🚀 Start Bot</button>
              <button class="btn btn-danger" onclick="stopBot()">⏹️ Stop Bot</button>
              <button class="btn btn-primary" onclick="testBot()">🧪 Test Bot</button>
            </div>
          </div>
          
          <div class="section">
            <h3>📊 Bot Statistics</h3>
            <div id="stats-display" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; margin: 20px 0;">
              <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>📨 Messages</h4>
                <span id="messages-received" style="font-size: 24px; font-weight: bold; color: #25D366;">0</span>
              </div>
              <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>🤖 AI Responses</h4>
                <span id="ai-responses" style="font-size: 24px; font-weight: bold; color: #25D366;">0</span>
              </div>
              <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>🖼️ Images</h4>
                <span id="images-processed" style="font-size: 24px; font-weight: bold; color: #1976d2;">0</span>
              </div>
              <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>👁️ Descriptions</h4>
                <span id="image-descriptions" style="font-size: 24px; font-weight: bold; color: #7b1fa2;">0</span>
              </div>
              <div style="background: #fff3cd; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>⏱️ Uptime</h4>
                <span id="uptime" style="font-size: 18px; font-weight: bold; color: #856404;">0s</span>
              </div>
              <div style="background: #fff8e1; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>📄 PDFs</h4>
                <span id="pdf-uploads" style="font-size: 24px; font-weight: bold; color: #f57c00;">0</span>
              </div>
              <div style="background: #f8d7da; padding: 15px; border-radius: 8px; text-align: center;">
                <h4>❌ Errors</h4>
                <span id="errors" style="font-size: 24px; font-weight: bold; color: #721c24;">0</span>
              </div>
            </div>
          </div>

          <div class="section">
            <h3>📊 Activity Logs</h3>
            <div id="logs" class="logs">
              <div>Bot ready to start...</div>
            </div>
          </div>
        </div>
      </div>
      
      <script>
        // File upload handling
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.getElementById('upload-area');
        const uploadContent = document.getElementById('upload-content');
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
          e.preventDefault();
          uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
          uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
          e.preventDefault();
          uploadArea.classList.remove('dragover');
          
          const files = e.dataTransfer.files;
          if (files.length > 0 && files[0].name.endsWith('.zip')) {
            handleFileUpload(files[0]);
          } else {
            alert('Please drop a ZIP file containing session data.');
          }
        });
        
        fileInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
          }
        });
        
        async function handleFileUpload(file) {
          const formData = new FormData();
          formData.append('sessionFile', file);
          
          uploadContent.innerHTML = '<h4>📤 Uploading session files...</h4><p>Please wait...</p>';
          
          try {
            const response = await fetch('/upload-session', {
              method: 'POST',
              body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
              uploadContent.innerHTML = '<h4>✅ Session files uploaded successfully!</h4><p>Bot will connect automatically.</p>';
              alert('✅ Session files uploaded successfully! Bot will connect automatically.');
            } else {
              uploadContent.innerHTML = '<h4>❌ Upload failed</h4><p>' + result.error + '</p>';
              alert('❌ Upload failed: ' + result.error);
            }
          } catch (error) {
            uploadContent.innerHTML = '<h4>❌ Upload error</h4><p>' + error.message + '</p>';
            alert('❌ Upload error: ' + error.message);
          }
          
          // Reset file input
          fileInput.value = '';
        }

        // PDF upload handling
        const pdfFileInput = document.getElementById('pdf-file-input');
        const pdfUploadArea = document.getElementById('pdf-upload-area');
        const pdfUploadContent = document.getElementById('pdf-upload-content');

        // PDF drag and drop functionality
        pdfUploadArea.addEventListener('dragover', (e) => {
          e.preventDefault();
          pdfUploadArea.classList.add('dragover');
        });

        pdfUploadArea.addEventListener('dragleave', () => {
          pdfUploadArea.classList.remove('dragover');
        });

        pdfUploadArea.addEventListener('drop', (e) => {
          e.preventDefault();
          pdfUploadArea.classList.remove('dragover');

          const files = e.dataTransfer.files;
          if (files.length > 0 && files[0].name.toLowerCase().endsWith('.pdf')) {
            handlePDFUpload(files[0]);
          } else {
            alert('Please drop a PDF file.');
          }
        });

        pdfFileInput.addEventListener('change', (e) => {
          if (e.target.files.length > 0) {
            handlePDFUpload(e.target.files[0]);
          }
        });

        async function handlePDFUpload(file) {
          const formData = new FormData();
          formData.append('pdfFile', file);

          pdfUploadContent.innerHTML = '<h4>📤 Uploading PDF...</h4><p>Processing document...</p>';

          try {
            const response = await fetch('/upload-pdf', {
              method: 'POST',
              body: formData
            });

            const result = await response.json();

            if (result.success) {
              pdfUploadContent.innerHTML = '<h4>✅ PDF uploaded successfully!</h4><p>Knowledge base updated.</p>';
              alert('✅ PDF uploaded successfully! Added ' + result.document.wordCount + ' words from ' + result.document.pageCount + ' pages.');
              // Refresh knowledge base info
              refreshKnowledgeBase();
            } else {
              pdfUploadContent.innerHTML = '<h4>❌ Upload failed</h4><p>' + result.error + '</p>';
              alert('❌ Upload failed: ' + result.error);
            }
          } catch (error) {
            pdfUploadContent.innerHTML = '<h4>❌ Upload error</h4><p>' + error.message + '</p>';
            alert('❌ Upload error: ' + error.message);
          }

          // Reset file input
          pdfFileInput.value = '';
        }

        async function refreshKnowledgeBase() {
          try {
            const response = await fetch('/knowledge-base');
            const data = await response.json();

            if (data.success) {
              document.getElementById('kb-documents').textContent = data.knowledgeBase.documentCount;
              document.getElementById('kb-words').textContent = data.knowledgeBase.documents.reduce((sum, doc) => sum + (doc.wordCount || 0), 0);
            }
          } catch (error) {
            console.error('Error refreshing knowledge base:', error);
          }
        }

        async function viewKnowledgeBase() {
          try {
            const response = await fetch('/knowledge-base');
            const data = await response.json();

            if (data.success) {
              let message = '📚 Knowledge Base (' + data.knowledgeBase.documentCount + ' documents)\\n\\n';

              if (data.knowledgeBase.documents.length === 0) {
                message += 'No documents uploaded yet.';
              } else {
                data.knowledgeBase.documents.forEach((doc, index) => {
                  message += (index + 1) + '. ' + doc.filename + '\\n';
                  message += '   📄 ' + doc.pageCount + ' pages, ' + doc.wordCount + ' words\\n';
                  message += '   📅 Uploaded: ' + new Date(doc.uploadedAt).toLocaleString() + '\\n\\n';
                });
              }

              alert(message);
            }
          } catch (error) {
            alert('❌ Error loading knowledge base: ' + error.message);
          }
        }
        
        async function startBot() {
          try {
            const response = await fetch('/start-bot', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Bot started successfully!');
            } else {
              alert('❌ Failed to start bot: ' + result.error);
            }
          } catch (error) {
            alert('❌ Error starting bot: ' + error.message);
          }
        }
        
        async function stopBot() {
          if (!confirm('Are you sure you want to stop the bot?')) {
            return;
          }
          
          try {
            const response = await fetch('/stop-bot', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Bot stopped successfully!');
            } else {
              alert('❌ Failed to stop bot: ' + result.error);
            }
          } catch (error) {
            alert('❌ Error stopping bot: ' + error.message);
          }
        }
        
        async function testBot() {
          try {
            const response = await fetch('/test-bot', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Test message sent! Check your WhatsApp.');
            } else {
              alert('❌ Test failed: ' + result.error);
            }
          } catch (error) {
            alert('❌ Test error: ' + error.message);
          }
        }
        
        async function refreshStatus() {
          try {
            const response = await fetch('/status');
            const data = await response.json();

            const statusDiv = document.getElementById('status-display');
            const logsDiv = document.getElementById('logs');
            const aiStatusBadge = document.getElementById('ai-status');

            // Update AI status badge
            if (data.aiEnabled) {
              aiStatusBadge.textContent = '🤖 AI Enabled';
              aiStatusBadge.style.background = '#28a745';
            } else {
              aiStatusBadge.textContent = '🔄 Echo Mode';
              aiStatusBadge.style.background = '#ffc107';
            }

            // Update statistics
            if (data.stats) {
              document.getElementById('messages-received').textContent = data.stats.messagesReceived || 0;
              document.getElementById('ai-responses').textContent = data.stats.aiResponses || 0;
              document.getElementById('images-processed').textContent = data.stats.imagesProcessed || 0;
              document.getElementById('image-descriptions').textContent = data.stats.imageDescriptions || 0;
              document.getElementById('uptime').textContent = data.stats.uptimeFormatted || '0s';
              document.getElementById('pdf-uploads').textContent = data.stats.pdfUploads || 0;
              document.getElementById('errors').textContent = data.stats.errors || 0;
            }

            // Update knowledge base info
            if (data.knowledgeBase) {
              document.getElementById('kb-documents').textContent = data.knowledgeBase.documentCount || 0;
              document.getElementById('kb-words').textContent = data.knowledgeBase.totalWords || 0;
              document.getElementById('kb-queries').textContent = data.stats.knowledgeQueries || 0;
            }

            // Update logs
            logsDiv.innerHTML = data.logs.map(log => '<div>' + log + '</div>').join('');
            logsDiv.scrollTop = logsDiv.scrollHeight;

            // Update status
            if (data.isConnected) {
              const aiStatus = data.aiEnabled ? 'AI-powered responses enabled' : 'Echo mode (AI not configured)';
              statusDiv.innerHTML = '<div class="status success"><h3>✅ Bot Connected!</h3><p>WhatsApp bot is online and ready. ' + aiStatus + '</p></div>';
            } else {
              statusDiv.innerHTML = '<div class="status warning"><h3>⏳ Bot Disconnected</h3><p>Bot is not connected to WhatsApp. Upload session files or start the bot.</p></div>';
            }
          } catch (error) {
            console.error('Status refresh error:', error);
          }
        }
        
        // Auto-refresh every 3 seconds
        setInterval(refreshStatus, 3000);
        
        // Initial load
        window.onload = function() {
          refreshStatus();
          refreshKnowledgeBase();
        };
      </script>
    </body>
    </html>
  `);
});

// Upload session files endpoint
app.post('/upload-session', sessionUpload.single('sessionFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: 'No file uploaded' });
    }

    addLog(`📁 Session file uploaded: ${req.file.originalname}`);

    // Extract session files
    const extracted = await extractSessionFiles(req.file.path);

    if (extracted) {
      addLog('✅ Session files extracted successfully');

      // Restart WhatsApp connection with new session
      setTimeout(() => {
        addLog('🔄 Restarting WhatsApp connection with uploaded session...');
        initializeWhatsApp();
      }, 2000);

      res.json({ success: true, message: 'Session files uploaded and extracted successfully' });
    } else {
      res.status(400).json({ success: false, error: 'Failed to extract session files from ZIP' });
    }
  } catch (error) {
    addLog(`❌ Upload error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Upload PDF files endpoint
app.post('/upload-pdf', pdfUpload.single('pdfFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: 'No PDF file uploaded' });
    }

    addLog(`📄 PDF file uploaded: ${req.file.originalname}`);

    // Process the PDF file
    const document = await processPDFFile(req.file.path, req.file.originalname);

    // Clean up uploaded file (we've extracted the text)
    try {
      fs.unlinkSync(req.file.path);
      addLog('🗑️ Cleaned up uploaded PDF file');
    } catch (cleanupError) {
      addLog(`⚠️ Could not clean up PDF file: ${cleanupError.message}`);
    }

    res.json({
      success: true,
      message: 'PDF uploaded and processed successfully',
      document: {
        id: document.id,
        filename: document.filename,
        pageCount: document.pageCount,
        wordCount: document.wordCount,
        uploadedAt: document.uploadedAt
      }
    });
  } catch (error) {
    addLog(`❌ PDF upload error: ${error.message}`);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
    }

    res.status(500).json({ success: false, error: error.message });
  }
});

// Start bot endpoint
app.post('/start-bot', async (req, res) => {
  try {
    addLog('🚀 Starting WhatsApp bot...');
    await initializeWhatsApp();
    res.json({ success: true, message: 'Bot started successfully' });
  } catch (error) {
    addLog(`❌ Start bot error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Stop bot endpoint
app.post('/stop-bot', async (req, res) => {
  try {
    addLog('⏹️ Stopping WhatsApp bot...');

    if (sock) {
      try {
        sock.end();
        sock = null;
        isConnected = false;
        addLog('✅ Bot stopped successfully');
      } catch (error) {
        addLog(`⚠️ Stop warning: ${error.message}`);
      }
    } else {
      addLog('⚠️ Bot was not running');
    }

    res.json({ success: true, message: 'Bot stopped successfully' });
  } catch (error) {
    addLog(`❌ Stop bot error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Test bot endpoint
app.post('/test-bot', async (req, res) => {
  if (!isConnected || !sock) {
    return res.status(400).json({ success: false, error: 'Bot is not connected to WhatsApp' });
  }

  try {
    addLog('🧪 Sending test message...');

    const aiStatus = aiEnabled ? '🤖 Gemini AI + Vision Enabled' : '🔄 Echo Mode';
    const testMessage = `🧪 *Test Message*\n\n` +
                       `✅ Bot is working perfectly!\n` +
                       `🕐 Time: ${new Date().toLocaleString()}\n` +
                       `🤖 Powered by Baileys\n` +
                       `👑 QueenAmdi Style Implementation\n` +
                       `${aiStatus}\n\n` +
                       `📊 Stats:\n` +
                       `• Messages Received: ${stats.messagesReceived}\n` +
                       `• Messages Processed: ${stats.messagesProcessed}\n` +
                       `• AI Responses: ${stats.aiResponses}\n` +
                       `• Images Processed: ${stats.imagesProcessed}\n` +
                       `• Image Descriptions: ${stats.imageDescriptions}\n` +
                       `• Errors: ${stats.errors}\n\n` +
                       `🖼️ *Image Analysis Available!*\n` +
                       `Send me any image and I'll describe it in detail!`;

    await sock.sendMessage(sock.user.id, { text: testMessage });

    addLog('✅ Test message sent successfully');
    res.json({ success: true, message: 'Test message sent to your WhatsApp' });
  } catch (error) {
    addLog(`❌ Test message error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Status endpoint
app.get('/status', (req, res) => {
  const uptime = Math.floor((new Date() - stats.startTime) / 1000);

  res.json({
    isConnected,
    aiEnabled,
    logs: logs.slice(-50),
    retryCount,
    maxRetries,
    sessionExists: fs.existsSync(sessionDir) && fs.readdirSync(sessionDir).length > 0,
    stats: {
      ...stats,
      uptime: uptime,
      uptimeFormatted: formatUptime(uptime)
    },
    geminiStatus: aiEnabled ? 'Connected' : 'Not configured',
    knowledgeBase: {
      documentCount: knowledgeBase.documents.length,
      lastUpdated: knowledgeBase.lastUpdated,
      totalWords: knowledgeBase.documents.reduce((sum, doc) => sum + (doc.wordCount || 0), 0)
    }
  });
});

// Knowledge base endpoint
app.get('/knowledge-base', (req, res) => {
  res.json({
    success: true,
    knowledgeBase: {
      documentCount: knowledgeBase.documents.length,
      lastUpdated: knowledgeBase.lastUpdated,
      documents: knowledgeBase.documents.map(doc => ({
        id: doc.id,
        filename: doc.filename,
        pageCount: doc.pageCount,
        wordCount: doc.wordCount,
        uploadedAt: doc.uploadedAt
      }))
    }
  });
});

// Delete document from knowledge base
app.delete('/knowledge-base/:documentId', async (req, res) => {
  try {
    const documentId = req.params.documentId;
    const documentIndex = knowledgeBase.documents.findIndex(doc => doc.id === documentId);

    if (documentIndex === -1) {
      return res.status(404).json({ success: false, error: 'Document not found' });
    }

    const document = knowledgeBase.documents[documentIndex];
    knowledgeBase.documents.splice(documentIndex, 1);
    knowledgeBase.lastUpdated = new Date();

    await saveKnowledgeBase();

    addLog(`🗑️ Deleted document: ${document.filename}`);
    res.json({ success: true, message: 'Document deleted successfully' });
  } catch (error) {
    addLog(`❌ Error deleting document: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Helper function to format uptime
function formatUptime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours}h ${minutes}m ${secs}s`;
}

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ success: false, error: 'File too large. Maximum size is 50MB.' });
    }
  }

  addLog(`❌ Express error: ${error.message}`);
  res.status(500).json({ success: false, error: error.message });
});

// Graceful shutdown handlers
process.on('SIGTERM', async () => {
  addLog('📴 SIGTERM received, shutting down gracefully...');
  if (sock) {
    try {
      sock.end();
    } catch (error) {
      addLog(`⚠️ Shutdown cleanup warning: ${error.message}`);
    }
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  addLog('📴 SIGINT received, shutting down gracefully...');
  if (sock) {
    try {
      sock.end();
    } catch (error) {
      addLog(`⚠️ Shutdown cleanup warning: ${error.message}`);
    }
  }
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  addLog(`❌ Uncaught exception: ${error.message}`);
  if (sock) {
    try {
      sock.end();
    } catch (destroyError) {
      // Ignore cleanup errors during crash
    }
  }
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  addLog(`❌ Unhandled rejection at: ${promise}, reason: ${reason}`);
});

// Start the server
app.listen(port, async () => {
  addLog(`🌐 WhatsApp Bot (Baileys) running on port ${port}`);
  addLog('🔗 Open your browser and navigate to this URL to manage the bot');
  addLog('✨ Using Baileys library with QueenAmdi style implementation');

  // Load existing knowledge base
  await loadKnowledgeBase();

  // Auto-start bot if session files exist
  const sessionFiles = fs.existsSync(sessionDir) ? fs.readdirSync(sessionDir) : [];
  if (sessionFiles.length > 0) {
    addLog('📁 Found existing session files, auto-starting bot...');
    setTimeout(() => {
      initializeWhatsApp();
    }, 3000);
  } else {
    addLog('📁 No session files found. Upload session files or scan QR code to connect.');
  }
});
