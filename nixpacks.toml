[phases.setup]
nixPkgs = ['nodejs_20', 'chromium', 'nss', 'freetype', 'freetype-dev', 'harfbuzz', 'ca-certificates', 'ttf-liberation']

[phases.install]
cmds = ['npm install --omit=dev']

[phases.build]
cmds = ['echo "No build step required"']

[start]
cmd = 'npm start'

[variables]
NODE_ENV = 'production'
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = 'true'
PUPPETEER_EXECUTABLE_PATH = '/nix/store/*/bin/chromium'
SESSION_DIR = '/tmp/whatsapp_session'
