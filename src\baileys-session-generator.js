const express = require('express');
const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { default: makeWASocket, DisconnectReason, useMultiFileAuthState } = require('@whiskeysockets/baileys');

const app = express();
const port = process.env.PORT || 3003;

// Application state
let qrCodeData = '';
let isConnected = false;
let sessionReady = false;
let logs = [];
let sock = null;
let retryCount = 0;
const maxRetries = 5;

// Session directory
const sessionDir = path.join(__dirname, '..', 'baileys_session');

// Utility functions
function addLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  logs.push(logMessage);
  console.log(logMessage);
  
  // Keep only last 50 logs
  if (logs.length > 50) {
    logs = logs.slice(-50);
  }
}

function ensureSessionDir() {
  if (!fs.existsSync(sessionDir)) {
    fs.mkdirSync(sessionDir, { recursive: true });
    addLog('📁 Session directory created');
  }
}

// Create session package for delivery
async function createSessionPackage() {
  try {
    addLog('📦 Creating session package for WhatsApp delivery...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const zipFileName = `whatsapp-baileys-session-${timestamp}.zip`;
    const zipPath = path.join(__dirname, '..', zipFileName);
    
    // Create ZIP archive
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    return new Promise((resolve, reject) => {
      output.on('close', () => {
        addLog(`✅ Session ZIP created: ${zipFileName} (${archive.pointer()} bytes)`);
        resolve({ zipPath, zipFileName });
      });
      
      archive.on('error', (err) => {
        addLog(`❌ Archive error: ${err.message}`);
        reject(err);
      });
      
      archive.pipe(output);
      
      // Add session files to archive
      if (fs.existsSync(sessionDir)) {
        archive.directory(sessionDir, 'baileys_session');
      }
      
      // Add instructions file
      const instructions = `# WhatsApp Bot Session Files (Baileys)

## Generated: ${new Date().toLocaleString()}

### Files Included:
- baileys_session/ - Complete session directory
- All authentication files for Baileys

### How to Use:
1. Extract this ZIP file to your bot's root directory
2. Make sure the 'baileys_session' folder is in the correct location
3. Your bot will connect automatically using these session files
4. No QR scanning required!

### Important Notes:
- Keep these files secure and private
- Don't share session files with anyone
- These files contain your WhatsApp authentication
- Generated using Baileys library for better stability

### Support:
- Generated by WhatsApp Bot Session Generator
- Using Baileys library for reliable connections
- No browser dependencies required

Happy botting! 🤖`;
      
      archive.append(instructions, { name: 'INSTRUCTIONS.txt' });
      archive.finalize();
    });
  } catch (error) {
    addLog(`❌ Error creating session package: ${error.message}`);
    throw error;
  }
}

// Send session files to user via WhatsApp
async function sendSessionFilesToUser() {
  try {
    if (!sock || !isConnected) {
      addLog('❌ WhatsApp not connected, cannot send session files');
      return;
    }
    
    const { zipPath, zipFileName } = await createSessionPackage();
    
    addLog('📱 Attempting to send session files via WhatsApp...');
    
    // Get user's JID (the account that scanned the QR)
    const userJid = sock.user.id;
    
    // Send welcome message
    const welcomeMessage = `🎉 *WhatsApp Bot Session Generated Successfully!*

Your Baileys-based WhatsApp bot session files are ready! 

📦 I'm sending you a ZIP file containing all the necessary session files to connect your bot without scanning QR codes.

🚀 *Next Steps:*
1. Deploy your bot to Railway or your platform
2. Upload the ZIP file I'm sending
3. Your bot connects automatically!

📋 Check the INSTRUCTIONS.txt file in the ZIP for detailed setup guide.

🔒 *Security Note:* Keep these files private and secure!

✨ *Baileys Advantages:*
- No browser dependencies
- Better stability and performance
- Direct WebSocket connection
- Lower resource usage`;

    await sock.sendMessage(userJid, { text: welcomeMessage });
    addLog('✅ Welcome message sent');
    
    // Send the ZIP file
    const fileBuffer = fs.readFileSync(zipPath);
    await sock.sendMessage(userJid, {
      document: fileBuffer,
      fileName: zipFileName,
      mimetype: 'application/zip',
      caption: `📁 *WhatsApp Bot Session Files (Baileys)*\n\n` +
               `Generated: ${new Date().toLocaleString()}\n` +
               `Library: Baileys (No browser required)\n\n` +
               `🔧 Upload this ZIP to your bot's web interface\n` +
               `✅ Your bot will connect automatically!\n\n` +
               `📖 See INSTRUCTIONS.txt for detailed setup guide`
    });
    
    addLog('✅ Session files sent successfully to user!');
    
    // Clean up ZIP file
    setTimeout(() => {
      try {
        fs.unlinkSync(zipPath);
        addLog('🗑️ Temporary ZIP file cleaned up');
      } catch (cleanupError) {
        addLog(`⚠️ Cleanup warning: ${cleanupError.message}`);
      }
    }, 30000); // Clean up after 30 seconds
    
  } catch (error) {
    addLog(`❌ Error sending session files: ${error.message}`);
    addLog(`📋 Error details: ${error.stack}`);
    addLog('💡 Manual download is available via the web interface');
  }
}

// Start Baileys WhatsApp session
async function startBaileysSession() {
  try {
    // Clean up any existing socket first
    if (sock) {
      try {
        sock.end();
      } catch (error) {
        addLog(`⚠️ Cleanup warning: ${error.message}`);
      }
      sock = null;
    }
    
    ensureSessionDir();
    addLog('🚀 Starting Baileys WhatsApp session generator...');
    
    // Use multi-file auth state
    const { state, saveCreds } = await useMultiFileAuthState(sessionDir);
    
    // Create Baileys socket with proper logger
    sock = makeWASocket({
      auth: state,
      printQRInTerminal: false,
      logger: {
        level: 'silent',
        trace: () => {},
        debug: () => {},
        info: () => {},
        warn: () => {},
        error: () => {},
        fatal: () => {},
        child: () => ({
          level: 'silent',
          trace: () => {},
          debug: () => {},
          info: () => {},
          warn: () => {},
          error: () => {},
          fatal: () => {}
        })
      }
    });
    
    // Handle QR code generation
    sock.ev.on('connection.update', async (update) => {
      const { connection, lastDisconnect, qr } = update;
      
      if (qr) {
        try {
          qrCodeData = await QRCode.toDataURL(qr);
          addLog('📱 QR Code generated successfully');
          retryCount = 0; // Reset retry counter on successful QR generation
        } catch (qrError) {
          addLog(`❌ QR Code generation error: ${qrError.message}`);
        }
      }
      
      if (connection === 'close') {
        const shouldReconnect = (lastDisconnect?.error)?.output?.statusCode !== DisconnectReason.loggedOut;
        
        isConnected = false;
        sessionReady = false;
        qrCodeData = '';
        
        if (sock) {
          sock = null;
        }
        
        addLog(`🔌 Connection closed: ${lastDisconnect?.error?.message || 'Unknown reason'}`);
        
        if (shouldReconnect) {
          retryCount++;
          
          if (retryCount >= maxRetries) {
            addLog(`❌ Maximum retry attempts (${maxRetries}) reached. Stopping automatic retries.`);
            addLog('🔧 Please try the following solutions:');
            addLog('   1. Click "Reset Session" and try again');
            addLog('   2. Restart the application');
            addLog('   3. Check your internet connection');
            addLog('   4. Try using a different device');
            retryCount = 0;
            return;
          }
          
          addLog(`🔄 Reconnecting... Attempt ${retryCount}/${maxRetries}`);
          setTimeout(() => {
            if (!sock) {
              startBaileysSession();
            }
          }, 5000);
        } else {
          addLog('🛑 Logged out, not reconnecting');
        }
      } else if (connection === 'open') {
        isConnected = true;
        sessionReady = true;
        qrCodeData = '';
        retryCount = 0;
        
        addLog('✅ WhatsApp connected! Preparing to send session files...');
        
        // Send session files after a short delay
        setTimeout(async () => {
          addLog('🔄 Starting automatic session file delivery...');
          try {
            await sendSessionFilesToUser();
          } catch (autoSendError) {
            addLog(`❌ Auto-delivery failed: ${autoSendError.message}`);
            addLog('💡 You can use the manual send button or download option');
          }
        }, 3000);
      }
    });
    
    // Handle credentials update
    sock.ev.on('creds.update', saveCreds);
    
    addLog('🔄 Baileys socket initialized, waiting for QR or connection...');
    
  } catch (error) {
    addLog(`❌ Error: ${error.message}`);
    
    // Clean up failed socket
    if (sock) {
      try {
        sock.end();
      } catch (destroyError) {
        // Ignore cleanup errors
      }
      sock = null;
    }
    
    retryCount++;
    
    if (retryCount >= maxRetries) {
      addLog(`❌ Maximum retry attempts (${maxRetries}) reached. Stopping automatic retries.`);
      addLog('🔧 Please try the following solutions:');
      addLog('   1. Click "Reset Session" and try again');
      addLog('   2. Restart the application');
      addLog('   3. Check your internet connection');
      addLog('   4. Try using a different device');
      retryCount = 0;
      return;
    }
    
    addLog(`🔄 Retrying in 10 seconds... Attempt ${retryCount}/${maxRetries}`);
    setTimeout(() => {
      if (!sock) {
        startBaileysSession();
      }
    }, 10000);
  }
}

// Express middleware
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>WhatsApp Bot Session Generator (Baileys)</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }
        .container { 
          background: white; 
          border-radius: 20px; 
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 800px; 
          width: 100%; 
          overflow: hidden;
        }
        .header {
          background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .content { padding: 40px; }
        .step { 
          background: #f8f9fa; 
          border-radius: 15px; 
          padding: 30px; 
          margin: 20px 0; 
          border-left: 5px solid #25D366;
        }
        .step h3 { color: #333; margin-bottom: 15px; font-size: 1.3em; }
        .step p { color: #666; line-height: 1.6; margin-bottom: 15px; }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          margin: 10px;
          background: #25D366;
          color: white;
          text-decoration: none;
          border-radius: 8px;
          font-weight: bold;
          border: none;
          cursor: pointer;
          font-size: 16px;
          transition: all 0.3s;
        }
        .btn:hover { background: #128C7E; transform: translateY(-2px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .btn-primary { background: #007bff; }
        .btn-primary:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #5a6268; }
        .status { 
          padding: 20px; 
          border-radius: 10px; 
          margin: 20px 0; 
          text-align: center;
        }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .qr-container { 
          text-align: center; 
          padding: 20px; 
          background: white; 
          border-radius: 10px; 
          margin: 20px 0;
        }
        .qr-container img { 
          max-width: 300px; 
          border: 2px solid #ddd; 
          border-radius: 10px;
        }
        .logs { 
          background: #1e1e1e; 
          color: #00ff00; 
          padding: 20px; 
          border-radius: 10px; 
          font-family: 'Courier New', monospace; 
          max-height: 300px; 
          overflow-y: auto; 
          margin: 20px 0;
        }
        .logs div { margin: 2px 0; font-size: 12px; }
        .feature-badge {
          display: inline-block;
          background: #007bff;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          margin: 2px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🚀 WhatsApp Bot Session Generator</h1>
          <p>Powered by Baileys - No Browser Required!</p>
          <div>
            <span class="feature-badge">🔥 Baileys Library</span>
            <span class="feature-badge">⚡ Direct WebSocket</span>
            <span class="feature-badge">🛡️ No Puppeteer</span>
            <span class="feature-badge">📱 Auto Delivery</span>
          </div>
        </div>
        
        <div class="content">
          <div class="step">
            <h3>Step 1: Generate Session Files</h3>
            <p>Click the button below to start generating your WhatsApp bot session files using the reliable Baileys library.</p>
            <div style="text-align: center;">
              <button class="btn" onclick="startSession()">🚀 Start Session Generation</button>
              <button class="btn btn-secondary" onclick="resetSession()">🔄 Reset Session</button>
            </div>
          </div>
          
          <div id="status-section">
            <div id="status-display"></div>
            <div id="qr-display"></div>
          </div>
          
          <div id="download-section" style="display: none;">
            <div class="step">
              <h3>Step 2: Session Files Ready! 📱</h3>
              <p>Your session files should have been sent to your WhatsApp automatically. If you didn't receive them, use the manual send option below.</p>
              <div style="text-align: center; margin: 20px 0;">
                <button class="btn btn-primary" onclick="testMessage()">🧪 Test WhatsApp</button>
                <button class="btn btn-success" onclick="sendToWhatsApp()">📱 Send to WhatsApp</button>
                <a href="/download" class="btn btn-success">📥 Download Backup Copy</a>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">Test delivery first, then try manual send if auto-delivery didn't work, or download a backup copy</p>
              </div>
            </div>
          </div>
          
          <div class="step">
            <h3>📊 Activity Logs</h3>
            <div id="logs" class="logs">
              <div>Waiting for session generation to start...</div>
            </div>
          </div>
        </div>
      </div>
      
      <script>
        async function startSession() {
          try {
            const response = await fetch('/start-session', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Session generation started! Watch the activity logs below.');
            } else {
              alert('❌ Failed to start session: ' + result.error);
            }
          } catch (error) {
            alert('❌ Error starting session: ' + error.message);
          }
        }
        
        async function resetSession() {
          if (!confirm('Are you sure you want to reset the session? This will clear all session data.')) {
            return;
          }
          
          try {
            const response = await fetch('/reset', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Session reset successfully!');
              location.reload();
            } else {
              alert('❌ Failed to reset session: ' + result.error);
            }
          } catch (error) {
            alert('❌ Error resetting session: ' + error.message);
          }
        }
        
        async function testMessage() {
          const btn = event.target;
          btn.disabled = true;
          btn.textContent = '🧪 Testing...';
          
          try {
            const response = await fetch('/test-message', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Test message sent! Check your WhatsApp to confirm delivery is working.');
            } else {
              alert('❌ Test failed: ' + result.error);
            }
          } catch (error) {
            alert('❌ Test error: ' + error.message);
          }
          
          btn.disabled = false;
          btn.textContent = '🧪 Test WhatsApp';
        }
        
        async function sendToWhatsApp() {
          const btn = event.target;
          btn.disabled = true;
          btn.textContent = '📱 Sending...';
          
          try {
            const response = await fetch('/send-to-whatsapp', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
              alert('✅ Session files sent to your WhatsApp! Check your messages.');
            } else {
              alert('❌ Failed to send to WhatsApp: ' + result.error);
            }
          } catch (error) {
            alert('❌ Error sending to WhatsApp: ' + error.message);
          }
          
          btn.disabled = false;
          btn.textContent = '📱 Send to WhatsApp';
        }
        
        async function refreshStatus() {
          try {
            const response = await fetch('/status');
            const data = await response.json();
            
            const statusDiv = document.getElementById('status-display');
            const qrDiv = document.getElementById('qr-display');
            const downloadDiv = document.getElementById('download-section');
            const logsDiv = document.getElementById('logs');
            
            // Update logs
            logsDiv.innerHTML = data.logs.map(log => '<div>' + log + '</div>').join('');
            logsDiv.scrollTop = logsDiv.scrollHeight;
            
            // Update status
            if (data.sessionReady) {
              statusDiv.innerHTML = '<div class="status success"><h3>✅ Session Files Sent!</h3><p>Your WhatsApp session files have been sent to your WhatsApp inbox. Check your messages!</p></div>';
              qrDiv.innerHTML = '';
              downloadDiv.style.display = 'block';
            } else if (data.isConnected) {
              statusDiv.innerHTML = '<div class="status success"><h3>✅ WhatsApp Connected!</h3><p>Generating session files... This may take a moment.</p><div style="text-align: center; margin: 15px 0;"><button class="btn btn-primary" onclick="testMessage()">🧪 Test WhatsApp Connection</button></div></div>';
              qrDiv.innerHTML = '';
            } else if (data.qrCode) {
              statusDiv.innerHTML = '<div class="status warning"><h3>📱 Scan QR Code</h3><p>Open WhatsApp → Settings → Linked Devices → Link a Device</p></div>';
              qrDiv.innerHTML = '<div class="qr-container"><img src="' + data.qrCode + '" alt="QR Code"><p>Scan this QR code with your WhatsApp</p></div>';
            } else {
              statusDiv.innerHTML = '<div class="status warning"><h3>⏳ Initializing...</h3><p>Starting session generation process...</p></div>';
              qrDiv.innerHTML = '';
            }
          } catch (error) {
            console.error('Status refresh error:', error);
          }
        }
        
        // Auto-refresh every 3 seconds
        setInterval(refreshStatus, 3000);
        
        // Initial load
        window.onload = refreshStatus;
      </script>
    </body>
    </html>
  `);
});

// Start session endpoint
app.post('/start-session', (req, res) => {
  try {
    addLog('🔄 Starting new session generation...');
    startBaileysSession();
    res.json({ success: true, message: 'Session generation started' });
  } catch (error) {
    addLog(`❌ Start session error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Status endpoint
app.get('/status', (req, res) => {
  let qrImage = '';
  if (qrCodeData) {
    qrImage = qrCodeData;
  }

  res.json({
    isConnected,
    sessionReady,
    qrCode: qrImage,
    logs: logs.slice(-20),
    retryCount,
    maxRetries
  });
});

// Test message endpoint
app.post('/test-message', async (req, res) => {
  if (!isConnected || !sock) {
    return res.status(400).json({ error: 'WhatsApp not connected' });
  }

  try {
    addLog('📱 Sending test message...');

    const userJid = sock.user.id;
    await sock.sendMessage(userJid, {
      text: `🧪 Test Message - ${new Date().toLocaleString()}\n\nThis is a test to verify WhatsApp delivery is working with Baileys library.`
    });

    addLog('✅ Test message sent successfully!');
    res.json({ success: true, message: 'Test message sent to WhatsApp' });
  } catch (error) {
    addLog(`❌ Test message error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Manual send session files endpoint
app.post('/send-to-whatsapp', async (req, res) => {
  if (!sessionReady) {
    return res.status(400).json({ error: 'Session not ready' });
  }

  try {
    addLog('📱 Manual WhatsApp delivery requested...');
    await sendSessionFilesToUser();
    res.json({ success: true, message: 'Session files sent to WhatsApp' });
  } catch (error) {
    addLog(`❌ Manual send error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Reset session endpoint
app.post('/reset', async (req, res) => {
  try {
    addLog('🗑️ Resetting session...');

    // Destroy socket if exists
    if (sock) {
      try {
        addLog('🔌 Stopping Baileys socket...');
        sock.end();
        sock = null;
        addLog('✅ Socket stopped successfully');
      } catch (destroyError) {
        addLog(`⚠️ Socket stop warning: ${destroyError.message}`);
        sock = null;
      }
    }

    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Clear session directory with retry logic
    if (fs.existsSync(sessionDir)) {
      let retries = 3;
      while (retries > 0) {
        try {
          fs.rmSync(sessionDir, { recursive: true, force: true });
          addLog('📁 Session directory cleared');
          break;
        } catch (deleteError) {
          retries--;
          if (retries === 0) {
            addLog(`⚠️ Could not delete some session files: ${deleteError.message}`);
          } else {
            addLog(`🔄 Retrying directory cleanup... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }
    }

    // Reset state
    qrCodeData = '';
    isConnected = false;
    sessionReady = false;
    retryCount = 0;
    logs = [];

    addLog('✅ Session reset complete');
    res.json({ success: true, message: 'Session reset successfully' });
  } catch (error) {
    addLog(`❌ Reset error: ${error.message}`);
    res.json({ success: false, error: error.message });
  }
});

// Stop session endpoint
app.post('/stop', async (req, res) => {
  try {
    addLog('⏹️ Stopping session...');

    // Stop socket if exists
    if (sock) {
      try {
        addLog('🔌 Stopping Baileys socket...');
        sock.end();
        sock = null;
        addLog('✅ Socket stopped successfully');
      } catch (destroyError) {
        addLog(`⚠️ Socket stop warning: ${destroyError.message}`);
        sock = null;
      }
    }

    // Reset connection state but keep session files
    qrCodeData = '';
    isConnected = false;
    sessionReady = false;

    addLog('✅ Session stopped');
    res.json({ success: true, message: 'Session stopped successfully' });
  } catch (error) {
    addLog(`❌ Stop error: ${error.message}`);
    res.json({ success: false, error: error.message });
  }
});

// Download session files endpoint
app.get('/download', async (req, res) => {
  try {
    if (!fs.existsSync(sessionDir)) {
      return res.status(404).json({ error: 'No session files found' });
    }

    addLog('📥 Manual download requested...');
    const { zipPath, zipFileName } = await createSessionPackage();

    res.download(zipPath, zipFileName, (err) => {
      if (err) {
        addLog(`❌ Download error: ${err.message}`);
      } else {
        addLog('✅ Session files downloaded successfully');
      }

      // Clean up ZIP file after download
      setTimeout(() => {
        try {
          fs.unlinkSync(zipPath);
          addLog('🗑️ Temporary ZIP file cleaned up');
        } catch (cleanupError) {
          addLog(`⚠️ Cleanup warning: ${cleanupError.message}`);
        }
      }, 5000);
    });
  } catch (error) {
    addLog(`❌ Download preparation error: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Graceful shutdown handlers
process.on('SIGTERM', async () => {
  addLog('📴 SIGTERM received, shutting down gracefully...');
  if (sock) {
    try {
      sock.end();
    } catch (error) {
      addLog(`⚠️ Shutdown cleanup warning: ${error.message}`);
    }
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  addLog('📴 SIGINT received, shutting down gracefully...');
  if (sock) {
    try {
      sock.end();
    } catch (error) {
      addLog(`⚠️ Shutdown cleanup warning: ${error.message}`);
    }
  }
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  addLog(`❌ Uncaught exception: ${error.message}`);
  if (sock) {
    try {
      sock.end();
    } catch (destroyError) {
      // Ignore cleanup errors during crash
    }
  }
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  addLog(`❌ Unhandled rejection at: ${promise}, reason: ${reason}`);
});

app.listen(port, () => {
  addLog(`🌐 Baileys WhatsApp Session Generator running on port ${port}`);
  addLog('🔗 Open your browser and navigate to this URL to generate session files');
  addLog('✨ Using Baileys library for better stability and performance');
});
