# Deployment Guide

## Railway.app Deployment

### Known Issues & Solutions

#### Puppeteer/Chrome Issues
The most common issue when deploying to Railway is P<PERSON>peteer failing to launch Chrome. This manifests as:

```
ProtocolError: Protocol error (Target.setAutoAttach): Target closed.
```

**Why this happens:**
- Cloud platforms have limited resources
- Chrome requires specific flags for headless operation
- Some platforms don't have all required dependencies

**Solutions implemented:**

1. **Enhanced Puppeteer Configuration:**
   - Added comprehensive Chrome flags for cloud compatibility
   - Increased timeout to 60 seconds
   - Added fallback executable path detection

2. **Nixpacks Configuration:**
   - `nixpacks.toml` specifies required system packages
   - Pre-installs Chromium and dependencies
   - Sets proper environment variables

3. **Graceful Error Handling:**
   - Web server starts even if WhatsApp fails
   - Automatic retry mechanism (30-second intervals)
   - Detailed error reporting in dashboard

4. **Alternative Web Version:**
   - Uses remote web version cache
   - Reduces dependency on local Chrome installation

### Deployment Steps

1. **Push to GitHub:**
   ```bash
   git add .
   git commit -m "Deploy to Railway"
   git push origin main
   ```

2. **Deploy to Railway:**
   - Connect your GitHub repository
   - Set environment variable: `GEMINI_API_KEY`
   - Railway will automatically detect and use `nixpacks.toml`

3. **Monitor Deployment:**
   - Check Railway logs for any issues
   - Visit the deployed URL to see the dashboard
   - If WhatsApp fails, the web interface will show troubleshooting info

### Environment Variables

Required:
- `GEMINI_API_KEY` - Your Google Gemini API key

Optional:
- `BOT_ROLE` - Custom bot personality
- `PUPPETEER_EXECUTABLE_PATH` - Custom Chrome path (auto-detected)

### Troubleshooting

#### If WhatsApp Connection Fails:
1. **Check the dashboard** - Visit your Railway URL
2. **Review logs** - Check Railway deployment logs
3. **Wait for retry** - App automatically retries every 30 seconds
4. **Manual restart** - Restart the Railway service

#### If the entire app fails:
1. **Check environment variables** - Ensure GEMINI_API_KEY is set
2. **Review build logs** - Look for npm install errors
3. **Check nixpacks** - Ensure all dependencies are installed

### Alternative Deployment Options

If Railway continues to have issues:

1. **Docker Deployment:**
   - Use the provided Dockerfile
   - Deploy to platforms like Render, Fly.io, or DigitalOcean

2. **VPS Deployment:**
   - Deploy to a VPS with full Chrome support
   - Use PM2 for process management

3. **Local Development:**
   - Run locally with `npm start`
   - Use ngrok for public access

### Performance Notes

- **Memory Usage:** WhatsApp Web.js + Chrome can use 200-500MB RAM
- **CPU Usage:** Moderate during message processing
- **Startup Time:** 30-60 seconds for full initialization
- **Network:** Requires stable internet for WhatsApp Web connection

### Success Indicators

✅ **Successful Deployment:**
- Web server starts on specified port
- Dashboard loads without errors
- QR code appears (or connection status shows)
- Health check endpoint responds

❌ **Failed Deployment:**
- App crashes immediately
- No web response
- Continuous restart loops
- Chrome/Puppeteer errors in logs
