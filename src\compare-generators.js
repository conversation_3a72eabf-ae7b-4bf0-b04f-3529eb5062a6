#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('\n🔍 WhatsApp Session Generator Comparison\n');

const comparison = [
  {
    feature: 'Library Used',
    whatsappWebJs: 'whatsapp-web.js + Puppeteer',
    baileys: '<PERSON><PERSON> (Direct WebSocket)',
    winner: 'baileys'
  },
  {
    feature: 'Browser Dependency',
    whatsappWebJs: '❌ Requires Chrome/Chromium',
    baileys: '✅ No browser needed',
    winner: 'baileys'
  },
  {
    feature: 'Memory Usage',
    whatsappWebJs: '~200MB + Browser overhead',
    baileys: '~50MB (75% less)',
    winner: 'baileys'
  },
  {
    feature: 'Startup Time',
    whatsappWebJs: '15-30 seconds',
    baileys: '3-5 seconds',
    winner: 'baileys'
  },
  {
    feature: 'Stability',
    whatsappWebJs: '⚠️ Browser disconnections',
    baileys: '✅ Rock solid',
    winner: 'baileys'
  },
  {
    feature: 'Version Conflicts',
    whatsappWebJs: '❌ Puppeteer compatibility',
    baileys: '✅ No conflicts',
    winner: 'baileys'
  },
  {
    feature: 'Connection Type',
    whatsappWebJs: 'Browser-based',
    baileys: 'Direct WebSocket',
    winner: 'baileys'
  },
  {
    feature: 'Resource Usage',
    whatsappWebJs: 'High (browser process)',
    baileys: 'Minimal',
    winner: 'baileys'
  },
  {
    feature: 'Error Handling',
    whatsappWebJs: 'Complex browser errors',
    baileys: 'Simple, predictable',
    winner: 'baileys'
  },
  {
    feature: 'Development',
    whatsappWebJs: 'JavaScript',
    baileys: 'TypeScript native',
    winner: 'baileys'
  }
];

// Print comparison table
console.log('┌─────────────────────┬─────────────────────────┬─────────────────────────┬────────┐');
console.log('│ Feature             │ whatsapp-web.js         │ Baileys                 │ Winner │');
console.log('├─────────────────────┼─────────────────────────┼─────────────────────────┼────────┤');

comparison.forEach(item => {
  const feature = item.feature.padEnd(19);
  const webjs = item.whatsappWebJs.padEnd(23);
  const baileys = item.baileys.padEnd(23);
  const winner = item.winner === 'baileys' ? 'Baileys' : 'WebJS';

  console.log(`│ ${feature} │ ${webjs} │ ${baileys} │ ${winner.padEnd(6)} │`);
});

console.log('└─────────────────────┴─────────────────────────┴─────────────────────────┴────────┘');

// Summary
const baileysWins = comparison.filter(item => item.winner === 'baileys').length;
const webjsWins = comparison.filter(item => item.winner === 'whatsapp-web.js').length;

console.log('\n📊 Summary:');
console.log(`✅ Baileys wins: ${baileysWins}/${comparison.length} categories`);
console.log(`❌ whatsapp-web.js wins: ${webjsWins}/${comparison.length} categories`);

console.log('\n🚀 Recommendation:');
console.log('Use Baileys for better performance, stability, and reliability!');

console.log('\n🎯 Quick Start:');
console.log('npm run baileys-generator  # Start Baileys generator (port 3003)');
console.log('npm run qr-generator       # Start WebJS generator (port 3001)');

console.log('\n🔗 URLs:');
console.log('Baileys:     http://localhost:3003');
console.log('WebJS:       http://localhost:3001');

// Check if generators are available
const baileysExists = fs.existsSync(path.join(__dirname, 'baileys-session-generator.js'));
const webjsExists = fs.existsSync(path.join(__dirname, 'qr-generator.js'));

console.log('\n📁 Available Generators:');
console.log(baileysExists ? '✅ Baileys generator available' : '❌ Baileys generator not found');
console.log(webjsExists ? '✅ WebJS generator available' : '❌ WebJS generator not found');

console.log('\n💡 Pro Tips:');
console.log('• Use Baileys for production environments');
console.log('• Baileys has no browser dependencies');
console.log('• Better for Railway/Heroku deployment');
console.log('• More stable for long-running bots');

console.log('\n🎉 Try Baileys now for a superior experience!\n');
